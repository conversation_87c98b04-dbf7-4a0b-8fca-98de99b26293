# Deployment Guide

This guide walks you through deploying the QR Code Redirect Backend to Cloudflare Workers.

## Prerequisites

1. **Cloudflare Account**: You need a Cloudflare account with Workers enabled
2. **Domain Setup**: Your domain should be managed by Cloudflare (for custom routes)
3. **D1 Database**: The main QRAnalytica D1 database should already be created
4. **Wrangler CLI**: Install globally with `npm install -g wrangler`

## Step-by-Step Deployment

### 1. Install Dependencies

```bash
cd qr-redirect-backend
npm install
```

### 2. Authenticate with Cloudflare

```bash
wrangler auth login
```

This will open a browser window for you to log in to your Cloudflare account.

### 3. Configure wrangler.toml

Update the `wrangler.toml` file with your specific settings:

```toml
# Update these values to match your setup
name = "qr-redirect-backend"
main = "src/index.ts"
compatibility_date = "2025-06-20"

# Update with your actual database ID
[[d1_databases]]
binding = "DB"
database_name = "qranalytica-astro"
database_id = "YOUR_ACTUAL_DATABASE_ID"

# Configure your custom domain routes
[[routes]]
pattern = "redirect.yourdomain.com/*"
zone_name = "yourdomain.com"
```

### 4. Test Locally (Optional)

```bash
npm run dev
```

Test the redirect functionality locally before deploying.

### 5. Deploy to Production

```bash
npm run deploy
```

### 6. Configure Custom Domain (Recommended)

1. **Add DNS Record**: In your Cloudflare DNS settings, add a CNAME record:
   - Name: `redirect` (or your preferred subdomain)
   - Target: `qr-redirect-backend.your-subdomain.workers.dev`

2. **Update Routes**: Make sure your `wrangler.toml` routes match your domain setup.

### 7. Test the Deployment

1. **Health Check**: Visit `https://redirect.yourdomain.com/health`
2. **Test Redirect**: Create a test QR code and try accessing it via the redirect URL

## Environment Configuration

### Production Variables

Set production environment variables using Wrangler:

```bash
# Set CORS origins for production
wrangler secret put ALLOWED_ORIGINS
# Enter: https://yourdomain.com,https://dashboard.yourdomain.com

# Set CORS max age
wrangler secret put CORS_MAX_AGE
# Enter: 86400
```

### Database Connection

The worker automatically connects to your D1 database using the binding configured in `wrangler.toml`. Make sure:

1. The `database_id` matches your actual D1 database
2. The database contains the required tables (`qr_codes`, `qr_code_scan_analytics`)
3. The binding name (`DB`) matches what's used in the code

## URL Structure

Once deployed, your QR codes should use URLs in this format:

- **Direct ID**: `https://redirect.yourdomain.com/qr/{qr_code_id}`
- **Custom Slug**: `https://redirect.yourdomain.com/{custom_slug}`

## Monitoring and Maintenance

### View Logs

```bash
npm run tail
```

### Monitor Performance

1. Visit the Cloudflare Workers dashboard
2. Select your `qr-redirect-backend` worker
3. View metrics, logs, and performance data

### Update Deployment

To deploy updates:

```bash
# Make your changes
# Test locally with: npm run dev
npm run deploy
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify the `database_id` in `wrangler.toml`
   - Check that the database exists and has the correct tables

2. **CORS Issues**
   - Update `ALLOWED_ORIGINS` environment variable
   - Check that your frontend domain is included

3. **Route Not Working**
   - Verify DNS settings in Cloudflare
   - Check that routes in `wrangler.toml` match your domain

4. **QR Code Not Found**
   - Ensure QR codes exist in the database
   - Check that the URL format matches expected patterns

### Debug Mode

For debugging, you can add console.log statements and view them with:

```bash
wrangler tail --format=pretty
```

## Security Considerations

1. **Database Access**: The worker only has access to the specific D1 database
2. **CORS**: Configure `ALLOWED_ORIGINS` to restrict access to your domains
3. **URL Validation**: The worker validates redirect URLs to prevent malicious redirects
4. **Rate Limiting**: Consider implementing rate limiting for production use

## Performance Optimization

1. **Smart Placement**: Enabled by default in `wrangler.toml`
2. **Caching**: Consider adding caching headers for static responses
3. **Database Indexes**: Ensure proper indexes exist on frequently queried columns

## Backup and Recovery

1. **Database Backups**: Use Wrangler to backup your D1 database regularly
2. **Code Versioning**: Keep your worker code in version control
3. **Configuration**: Document any custom configuration changes
