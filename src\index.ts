import { Env, ScanAnalytics } from './types';
import { 
  parseUserAgent, 
  getGeoLocation, 
  getClientIP, 
  generateAnalyticsId,
  isValidRedirectUrl,
  createCorsHeaders
} from './utils';
import { 
  getQRCodeById, 
  getQRCodeBySlug, 
  storeScanAnalytics,
  checkDatabaseHealth
} from './database';

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);
    const origin = request.headers.get('Origin');
    const corsHeaders = createCorsHeaders(origin, env.ALLOWED_ORIGINS);

    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: corsHeaders
      });
    }

    // Health check endpoint
    if (url.pathname === '/health') {
      const isHealthy = await checkDatabaseHealth(env.DB);
      return new Response(
        JSON.stringify({ 
          status: isHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString()
        }),
        {
          status: isHealthy ? 200 : 503,
          headers: {
            'Content-Type': 'application/json',
            ...Object.fromEntries(corsHeaders.entries())
          }
        }
      );
    }

    // Only handle GET requests for redirects
    if (request.method !== 'GET') {
      return new Response('Method not allowed', { 
        status: 405,
        headers: corsHeaders
      });
    }

    // Extract QR code identifier from URL path
    // Expected formats: /qr/{id} or /{custom_slug}
    const pathSegments = url.pathname.split('/').filter(segment => segment.length > 0);
    
    if (pathSegments.length === 0) {
      return new Response('QR code identifier required', { 
        status: 400,
        headers: corsHeaders
      });
    }

    let qrCodeId: string;
    let qrCode;

    let redirectUrl: string;
    // Check if it's a direct ID access (/qr/{id}) or custom slug (/{slug})
    if (pathSegments[0] === 'qr' && pathSegments.length === 2) {
      qrCodeId = pathSegments[1];
      qrCode = await getQRCodeById(env.DB, qrCodeId);
      redirectUrl = JSON.parse(qrCode?.data || '{}').data
    } else if (pathSegments.length === 1) {
      // Try to find by custom slug
      const slug = pathSegments[0];
      qrCode = await getQRCodeBySlug(env.DB, slug);
      qrCodeId = qrCode?.id || slug;
      redirectUrl = JSON.parse(qrCode?.data || '{}').data
    } else {
      return new Response('Invalid QR code URL format', { 
        status: 400,
        headers: corsHeaders
      });
    }

    // If QR code not found, return 404
    if (!qrCode) {
      return new Response('QR code not found', { 
        status: 404,
        headers: corsHeaders
      });
    }
 

    // Validate redirect URL
    if (!isValidRedirectUrl(redirectUrl)) {
      return new Response('Invalid redirect URL', { 
        status: 400,
        headers: corsHeaders
      });
    }

    // Collect analytics data
    const now = new Date().toISOString();
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const referrer = request.headers.get('Referer');
    const deviceInfo = parseUserAgent(userAgent);
    const geoLocation = getGeoLocation(request);

    const analyticsData: ScanAnalytics = {
      id: generateAnalyticsId(),
      qr_code_id: qrCode.id,
      scan_time: now,
      ip: clientIP,
      user_agent: userAgent,
      referrer: referrer,
      lat: geoLocation.lat,
      lon: geoLocation.lon,
      city: geoLocation.city,
      country: geoLocation.country,
      device: deviceInfo.device,
      os: deviceInfo.os,
      browser: deviceInfo.browser,
      created_at: now
    };

    // Store analytics data asynchronously (don't wait for it to complete)
    ctx.waitUntil(storeScanAnalytics(env.DB, analyticsData));

    // Perform the redirect
    return Response.redirect(redirectUrl, 302);
  }
};
