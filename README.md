# QR Code Redirect Backend

A lightweight Cloudflare Workers backend service that handles QR code redirections while capturing comprehensive analytics data.

## Features

- **Fast Redirects**: Minimal latency QR code redirections
- **Analytics Tracking**: Captures detailed scan analytics including:
  - Timestamp and IP address
  - User agent and device information
  - Geographic location (via Cloudflare headers)
  - <PERSON><PERSON><PERSON> and OS detection
  - Referrer information
- **Database Integration**: Connects to Cloudflare D1 database
- **CORS Support**: Configurable CORS headers
- **Health Monitoring**: Built-in health check endpoint
- **TypeScript**: Full type safety and IntelliSense support

## Architecture

This backend service is designed to be completely separate from the main QRAnalytica dashboard application. It serves as a dedicated redirect service that:

1. Receives QR code scan requests
2. Looks up QR code data in the D1 database
3. Captures and stores analytics data
4. Redirects users to their intended destination

## Setup

### Prerequisites

- Node.js 18+ installed
- Cloudflare account with Workers and D1 access
- Wrangler CLI installed globally: `npm install -g wrangler`

### Installation

1. Navigate to the project directory:
   ```bash
   cd qr-redirect-backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Authenticate with Cloudflare:
   ```bash
   wrangler auth login
   ```

4. Update the `wrangler.toml` file with your specific configuration:
   - Update the `database_id` to match your D1 database
   - Configure your custom domain routes
   - Adjust environment variables as needed

### Development

Start the development server:
```bash
npm run dev
```

This will start a local development server where you can test the redirect functionality.

### Deployment

Deploy to Cloudflare Workers:
```bash
npm run deploy
```

### Configuration

#### Environment Variables

- `ALLOWED_ORIGINS`: CORS allowed origins (default: "*")
- `CORS_MAX_AGE`: CORS max age in seconds (default: "86400")

#### Routes

The service supports two URL patterns:

1. **Direct ID Access**: `/qr/{qr_code_id}`
   - Example: `https://redirect.qranalytica.com/qr/abc123`

2. **Custom Slug Access**: `/{custom_slug}`
   - Example: `https://redirect.qranalytica.com/my-custom-qr`

#### Database Schema

The service expects the following D1 database tables (should already exist in your main QRAnalytica database):

- `qr_codes`: Contains QR code definitions
- `qr_code_scan_analytics`: Stores scan analytics data

## API Endpoints

### `GET /health`
Health check endpoint that verifies database connectivity.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-20T10:30:00.000Z"
}
```

### `GET /qr/{id}` or `GET /{slug}`
Redirect endpoint that:
1. Looks up the QR code
2. Captures analytics data
3. Redirects to the target URL

**Response:** HTTP 302 redirect to target URL

## Analytics Data Captured

For each QR code scan, the following data is captured:

- **Timestamp**: When the scan occurred
- **IP Address**: Client IP (via CF-Connecting-IP header)
- **User Agent**: Full user agent string
- **Device Info**: Parsed device type, OS, and browser
- **Geographic Data**: Country and city (via Cloudflare headers)
- **Referrer**: Where the user came from

## Error Handling

The service includes comprehensive error handling for:

- Invalid QR code IDs/slugs
- Database connection issues
- Invalid redirect URLs
- CORS violations

## Monitoring

Use Wrangler's built-in monitoring:

```bash
# View real-time logs
npm run tail

# View analytics in Cloudflare dashboard
```

## Security

- Only allows HTTP/HTTPS redirects
- Validates all input parameters
- Uses prepared statements for database queries
- Implements proper CORS headers

## Performance

- Optimized for minimal latency
- Uses Cloudflare's Smart Placement
- Asynchronous analytics storage
- Efficient database queries with indexes
