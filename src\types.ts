// Environment bindings interface
export interface Env {
  DB: D1Database;
  ALLOWED_ORIGINS: string;
  CORS_MAX_AGE: string;
}

// QR Code database record
export interface QRCode {
  id: string;
  user_id: string | null;
  name: string | null;
  data: string;
  dynamic: number;
  tracking_domain: string | null;
  custom_slug: string | null;
  redirect_url: string | null;
  created_at: string;
}

// Analytics data to be stored
export interface ScanAnalytics {
  id: string;
  qr_code_id: string;
  scan_time: string;
  ip: string | null;
  user_agent: string | null;
  referrer: string | null;
  lat: number | null;
  lon: number | null;
  city: string | null;
  country: string | null;
  device: string | null;
  os: string | null;
  browser: string | null;
  created_at: string;
}

// Geolocation data from IP
export interface GeoLocation {
  lat: number | null;
  lon: number | null;
  city: string | null;
  country: string | null;
}

// Device/Browser information parsed from User-Agent
export interface DeviceInfo {
  device: string | null;
  os: string | null;
  browser: string | null;
}

// API Response types
export interface RedirectResponse {
  success: boolean;
  redirect_url?: string;
  error?: string;
}

export interface AnalyticsResponse {
  success: boolean;
  analytics_id?: string;
  error?: string;
}
