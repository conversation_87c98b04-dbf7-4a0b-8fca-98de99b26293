import { DeviceInfo, GeoLocation } from './types';

/**
 * Parse User-Agent string to extract device, OS, and browser information
 */
export function parseUserAgent(userAgent: string | null): DeviceInfo {
  if (!userAgent) {
    return { device: null, os: null, browser: null };
  }

  const ua = userAgent.toLowerCase();
  
  // Detect device type
  let device = 'Desktop';
  if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
    device = 'Mobile';
  } else if (ua.includes('tablet') || ua.includes('ipad')) {
    device = 'Tablet';
  }

  // Detect operating system
  let os = 'Unknown';
  if (ua.includes('windows')) {
    os = 'Windows';
  } else if (ua.includes('mac os x') || ua.includes('macos')) {
    os = 'macOS';
  } else if (ua.includes('linux')) {
    os = 'Linux';
  } else if (ua.includes('android')) {
    os = 'Android';
  } else if (ua.includes('ios') || ua.includes('iphone') || ua.includes('ipad')) {
    os = 'iOS';
  }

  // Detect browser
  let browser = 'Unknown';
  if (ua.includes('chrome') && !ua.includes('edge') && !ua.includes('opr')) {
    browser = 'Chrome';
  } else if (ua.includes('firefox')) {
    browser = 'Firefox';
  } else if (ua.includes('safari') && !ua.includes('chrome')) {
    browser = 'Safari';
  } else if (ua.includes('edge')) {
    browser = 'Edge';
  } else if (ua.includes('opr') || ua.includes('opera')) {
    browser = 'Opera';
  }

  return { device, os, browser };
}

/**
 * Get geolocation data from IP address using Cloudflare's CF-IPCountry header
 * and other available headers
 */
export function getGeoLocation(request: Request): GeoLocation {
  const headers = request.headers;
  
  // Cloudflare provides country information
  const country = headers.get('CF-IPCountry') || null;
  
  // Cloudflare also provides city information in some cases
  const city = headers.get('CF-IPCity') || null;
  
  // For latitude and longitude, we would need to use an external service
  // or maintain a database of city coordinates. For now, we'll set them as null
  // and they can be populated later with a geolocation service if needed
  const lat = null;
  const lon = null;

  return { lat, lon, city, country };
}

/**
 * Get client IP address from request headers
 */
export function getClientIP(request: Request): string | null {
  // Try various headers that might contain the real IP
  const headers = request.headers;
  
  // Cloudflare provides the real IP in CF-Connecting-IP
  const cfIP = headers.get('CF-Connecting-IP');
  if (cfIP) return cfIP;
  
  // Fallback to other common headers
  const xForwardedFor = headers.get('X-Forwarded-For');
  if (xForwardedFor) {
    // X-Forwarded-For can contain multiple IPs, take the first one
    return xForwardedFor.split(',')[0].trim();
  }
  
  const xRealIP = headers.get('X-Real-IP');
  if (xRealIP) return xRealIP;
  
  return null;
}

/**
 * Generate a unique ID for analytics records
 */
export function generateAnalyticsId(): string {
  // Simple UUID v4 generation
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Validate if a URL is safe for redirection
 */
export function isValidRedirectUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    // Only allow http and https protocols
    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Create CORS headers for responses
 */
export function createCorsHeaders(origin: string | null, allowedOrigins: string): Headers {
  const headers = new Headers();
  
  // Handle CORS
  if (allowedOrigins === '*') {
    headers.set('Access-Control-Allow-Origin', '*');
  } else if (origin && allowedOrigins.split(',').includes(origin)) {
    headers.set('Access-Control-Allow-Origin', origin);
  }
  
  headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  headers.set('Access-Control-Max-Age', '86400');
  
  return headers;
}
