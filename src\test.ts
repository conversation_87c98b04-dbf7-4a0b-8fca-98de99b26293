import { describe, it, expect } from 'vitest';
import { parseUserAgent, getClientIP, isValidRedirectUrl, generateAnalyticsId } from './utils';

describe('Utility Functions', () => {
  describe('parseUserAgent', () => {
    it('should parse Chrome on Windows correctly', () => {
      const ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
      const result = parseUserAgent(ua);
      
      expect(result.browser).toBe('Chrome');
      expect(result.os).toBe('Windows');
      expect(result.device).toBe('Desktop');
    });

    it('should parse Safari on iPhone correctly', () => {
      const ua = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1';
      const result = parseUserAgent(ua);
      
      expect(result.browser).toBe('Safari');
      expect(result.os).toBe('iOS');
      expect(result.device).toBe('Mobile');
    });

    it('should handle null user agent', () => {
      const result = parseUserAgent(null);
      
      expect(result.browser).toBe(null);
      expect(result.os).toBe(null);
      expect(result.device).toBe(null);
    });
  });

  describe('getClientIP', () => {
    it('should extract IP from CF-Connecting-IP header', () => {
      const request = new Request('https://example.com', {
        headers: {
          'CF-Connecting-IP': '***********'
        }
      });
      
      const ip = getClientIP(request);
      expect(ip).toBe('***********');
    });

    it('should fallback to X-Forwarded-For header', () => {
      const request = new Request('https://example.com', {
        headers: {
          'X-Forwarded-For': '***********, ********'
        }
      });
      
      const ip = getClientIP(request);
      expect(ip).toBe('***********');
    });
  });

  describe('isValidRedirectUrl', () => {
    it('should accept valid HTTPS URLs', () => {
      expect(isValidRedirectUrl('https://example.com')).toBe(true);
      expect(isValidRedirectUrl('https://example.com/path?query=1')).toBe(true);
    });

    it('should accept valid HTTP URLs', () => {
      expect(isValidRedirectUrl('http://example.com')).toBe(true);
    });

    it('should reject invalid protocols', () => {
      expect(isValidRedirectUrl('javascript:alert(1)')).toBe(false);
      expect(isValidRedirectUrl('data:text/html,<script>alert(1)</script>')).toBe(false);
      expect(isValidRedirectUrl('ftp://example.com')).toBe(false);
    });

    it('should reject malformed URLs', () => {
      expect(isValidRedirectUrl('not-a-url')).toBe(false);
      expect(isValidRedirectUrl('')).toBe(false);
    });
  });

  describe('generateAnalyticsId', () => {
    it('should generate valid UUID format', () => {
      const id = generateAnalyticsId();
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      
      expect(id).toMatch(uuidRegex);
    });

    it('should generate unique IDs', () => {
      const id1 = generateAnalyticsId();
      const id2 = generateAnalyticsId();
      
      expect(id1).not.toBe(id2);
    });
  });
});
